<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'dashboard';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

$route['language/(:any)']           = 'language/index/$1';
$route['perspective/(:any)/(:any)'] = 'perspective/index/$1/$2';
$route['perspective/(:any)']        = 'perspective/index/$1';
$route['documents/autosave']        = 'documents/autosave';
$route['documents/search']          = 'documents/search';
$route['documents/delete']          = 'documents/delete';
$route['documents/upload']          = 'documents/upload';
$route['documents/elfinder']        = 'documents/elfinder';
$route['documents/archive']         = 'documents/archive';
$route['documents/unarchive']         = 'documents/unarchive';
$route['documents/delete_draft']    = 'documents/delete_draft';
$route['documents/education']       = 'documents/education';
$route['documents/draft/(:any)']    = 'documents/draft/$1';
$route['documents/approve_multiple']= 'documents/approve_multiple';
$route['documents/readlog/(:any)']  = 'documents/readlog/$1';
$route['documents/update_edit_date/(:any)']  = 'documents/update_edit_date/$1';
$route['documents/(:any)']          = 'documents/index/$1';
$route['docs/help']                 = 'documents/help';
$route['docs/standards']            = 'documents/standards';
$route['docs/orna_analys_manual']   = 'documents/orna_analys_manual';
$route['docs/orna_updates']         = 'documents/orna_updates';
$route['tasks/create']              = 'tasks/create';
$route['tasks/archive']             = 'tasks/archive';
$route['tasks/update/(:any)']       = 'tasks/update/$1';
$route['tasks/draft/(:any)']        = 'tasks/draft/$1';
$route['tasks/add_progress/(:any)'] = 'tasks/add_progress/$1';
$route['tasks/edit_progress/(:any)/(any)'] = 'tasks/edit_progress/$1/$2';
$route['tasks/(:any)']              = 'tasks/index/$1';
$route['tasks_list']                = 'tasks/list';

$route['folder/(:any)']             = 'folder/index/$1';
$route['post/download/(:any)']      = 'page/download/post/$1';
$route['post/create/(:any)']        = 'page/create/$1';
$route['post/update/(:any)/(:any)'] = 'page/update/$1/$2';
$route['post/readlog/(:any)/(:any)'] = 'page/readlog/$1/$2';
$route['post/update_edit_date/(:any)']  = 'page/update_edit_date/$1';
$route['post/comment/(update|delete)/(:any)'] = 'page/comment/$1/$2';
$route['post/(:any)/(:any)']        = 'page/post/$1/$2';
$route['page/download/(:any)']      = 'page/download/page/$1';
$route['page/(:any)/(:any)']        = 'page/index/$1/$2';
$route['page/(:any)']               = 'page/index/$1';
$route['reports/education/(:any)']  = 'reports/education/$1';
$route['reports/education']         = 'reports/education';
$route['reports/stats']             = 'admin/users/stats';
$route['reports/login']             = 'reports/login';
$route['reports/documents']         = 'reports/documents';
$route['reports/checklists']        = 'reports/checklists';
$route['reports/deviation']         = 'reports/deviation';
$route['reports/documents_overview']= 'reports/documents_overview';
$route['reports/(:any)/(:any)']     = 'reports/index/$1/$2';
$route['reports/(:any)']            = 'reports/index/$1';
$route['reports']                   = 'reports/display';
$route['documents_overview']        = 'dashboard/documents_overview';
$route['eventanalysis']             = 'eventanalysis/display';
$route['riskassessments']           = 'riskassessments/display';
$route['deviation']                 = 'deviation/display';
$route['admin/posts']               = 'admin/posts/display';
$route['admin/pages']               = 'admin/pages/display';
$route['admin']                     = 'admin/dashboard/index';

$route['verify']                     = 'login/verify';
$route['user/2fa']                   = 'user/enable_2fa';

$route['documentcallback/download_path/(:any)/(:any)/(:any)'] = 'documentcallback/download_path/$1/$2/$3';
$route['documentcallback/download_path/(:any)/(:any)'] = 'documentcallback/download_path/$1/$2';
$route['documentcallback/(:any)/(:any)/(:any)'] = 'documentcallback/index/$1/$2/$3';
$route['documentcallback/(:any)/(:any)'] = 'documentcallback/index/$1/$2';
$route['doceditor/(:any)/(:any)'] = 'documenteditor/index/$1/$2';

$route['viewdocument/(:any)']    = 'documentview/view/$1';
$route['viewdeviation/(:any)']    = 'deviationview/view/$1';
$route['vieweventanalysis/(:any)']    = 'eventanalysisview/view/$1';
$route['viewriskassessments/(:any)']    = 'riskassessmentsview/view/$1';

$route['api/status'] = 'api/authApi/status';
$route['api/auth/login'] = 'api/authApi/login';
$route['api/auth/refresh'] = 'api/authApi/refresh';
$route['api/deviation/search'] = 'api/deviationApi/search';
$route['api/deviation/fields'] = 'api/deviationApi/getFields';
$route['api/deviation/upload'] = 'api/deviationApi/upload';
$route['api/deviation/add'] = 'api/deviationApi/add';
$route['api/deviation/view/(:any)'] = 'api/deviationApi/view/$1';

$route['api/menu'] = 'api/MenuApi/getMainMenus';
$route['api/menu/(:any)'] = 'api/MenuApi/getMenuStructure/$1';
$route['api/folder/(:any)'] = 'api/MenuApi/getFolderDocuments/$1';
$route['api/document/(:any)'] = 'api/MenuApi/getDocumentById/$1';
$route['api/documents/search'] = 'api/MenuApi/searchDocuments';

$route['api/checklist'] = 'api/checklistApi/list';
$route['api/checklist/(:any)/submissions'] = 'api/checklistApi/submissions/$1';
$route['api/checklist/(:any)/questions'] = 'api/checklistApi/questions/$1';
$route['api/checklist/(:any)/submit'] = 'api/checklistApi/submit/$1';

