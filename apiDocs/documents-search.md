# Documents Search API

## Overview
The Documents Search API provides optimized search functionality for documents within the system. It performs database-level searches across document names, descriptions, and content while respecting user permissions and access controls.

## Endpoint

### GET /api/documents/search
Search for documents by name, description, or content based on user access permissions.

**URL:** `/api/documents/search`
**Method:** `GET`
**Authentication:** Required (JWT <PERSON> token)

## Request Parameters

### Query Parameters
- `s` (string, required): Search query string
  - **Minimum length:** 3 characters
  - **Maximum length:** 64 characters
  - **Allowed characters:** Letters (a-z, A-Z, åäöÅÄÖ), numbers (0-9), spaces, dashes (-), colons (:)
  - **Example:** `quality management`, `SOP-001`, `ISO:9001`

## Response Format

### Success Response (200 OK)
```json
{
  "success": true,
  "search_query": "quality management",
  "documents": [
    {
      "id": "01935c8d-c704-7d58-94fe-d25ef0bf76b4",
      "name": "Quality Management System Overview",
      "description": "Comprehensive overview of our quality management processes",
      "type": "document",
      "status": "published",
      "document_type": "Quality Regulation",
      "document_category": "Policy Documents",
      "folder_name": "Quality Management",
      "menu_name": "Main Quality Menu",
      "created_date": "2024-01-15 10:30:00",
      "edited_date": "2024-02-20 14:45:00",
      "last_revised": "2024-02-20",
      "valid_until": "2025-01-15",
      "created_by": "John Doe",
      "file_ext": ".docx"
    }
  ],
  "total": 1
}
```

### Empty Results Response (200 OK)
```json
{
  "success": true,
  "search_query": "nonexistent term",
  "documents": [],
  "total": 0,
  "message": "No documents found matching your search criteria"
}
```

## Response Fields

### Document Object Fields
- `id` (string): Document UUID
- `name` (string): Document name/title
- `description` (string): Document description
- `type` (string): Always "document"
- `status` (string): Document status (typically "published")
- `document_type` (string): Human-readable document type name
- `document_category` (string): Human-readable document category name
- `folder_name` (string): Name of the containing folder
- `menu_name` (string): Name of the parent menu
- `created_date` (string): Creation timestamp (YYYY-MM-DD HH:MM:SS)
- `edited_date` (string): Last edit timestamp (YYYY-MM-DD HH:MM:SS)
- `last_revised` (string): Last revision date (YYYY-MM-DD)
- `valid_until` (string): Document expiration date (YYYY-MM-DD)
- `created_by` (string): Name of document creator
- `file_ext` (string): File extension of main attachment (e.g., ".docx", ".pdf")

## Search Features

### Search Capabilities
- **Multi-field search:** Searches across document name, description, and content
- **Multi-word support:** Space-separated terms are treated as AND conditions
- **Case-insensitive:** Search is not case-sensitive
- **Content search:** Includes search in document content (content_clean field)
- **Performance optimized:** Uses database-level search with proper indexing
- **Result limit:** Returns maximum 100 results, ordered by document name

### Security & Permissions
- **User-based access control:** Only returns documents the user has access to
- **Menu-level permissions:** Respects user's menu group permissions
- **Department/position filtering:** Filters based on user's department and position groups
- **Published documents only:** Only searches published documents

## Error Responses

### 400 Bad Request - Missing Search Parameter
```json
{
  "success": false,
  "error": "Search parameter is required",
  "message": "Parameter \"s\" is missing or empty"
}
```

### 400 Bad Request - Search Query Too Short
```json
{
  "success": false,
  "error": "Search query too short",
  "message": "Search query must be at least 3 characters long"
}
```

### 400 Bad Request - Search Query Too Long
```json
{
  "success": false,
  "error": "Search query too long",
  "message": "Search query must be 64 characters or less"
}
```

### 400 Bad Request - Invalid Characters
```json
{
  "success": false,
  "error": "Invalid search query format",
  "message": "Search query can only contain letters, numbers, spaces, dashes, and colons"
}
```

### 401 Unauthorized - Invalid Token
```json
{
  "success": false,
  "error": "Unauthorized",
  "message": "Invalid token"
}
```

### 401 Unauthorized - Missing Token
```json
{
  "success": false,
  "error": "Unauthorized",
  "message": "Authorization header missing"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "error": "Internal server error",
  "message": "An error occurred while searching documents"
}
```

## Usage Examples

### Basic Search
```bash
curl -X GET "https://api.example.com/api/documents/search?s=quality" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Multi-word Search
```bash
curl -X GET "https://api.example.com/api/documents/search?s=quality%20management%20system" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Search with Special Characters
```bash
curl -X GET "https://api.example.com/api/documents/search?s=SOP-001" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Performance Notes

- **Database optimization:** Uses JOIN queries with proper indexing for fast results
- **Result limiting:** Automatically limits to 100 results to prevent performance issues
- **Caching:** Document types and categories are cached during request processing
- **Error handling:** Graceful error handling with detailed logging for debugging

## Implementation Details

### Search Algorithm
1. **Input validation:** Validates search query format and length (3-64 characters)
2. **Permission check:** Retrieves user's accessible menus based on group permissions
3. **Search execution:** Uses existing `document_model->search()` method for consistency
4. **Multi-field search:** Searches across name, description, content, and content_clean fields
5. **Result processing:** Converts binary UUIDs and enriches with type/category names
6. **Response formatting:** Returns standardized JSON response with all required fields

### Technical Implementation
- **Leverages existing code:** Uses the proven `Document_model->search()` method
- **Multi-word support:** Splits search query by spaces for individual term matching
- **Group-based permissions:** Integrates with existing user group and ACL systems
- **Menu context:** Sets accessible menus in document model for proper permission filtering
- **Result limit:** Inherent limit from existing search method prevents performance issues

### Security Considerations
- All database queries use parameterized statements to prevent SQL injection
- User permissions are validated before any search operations
- Only published documents are included in search results
- Respects existing group-based access control system
- Company-level isolation ensures data separation